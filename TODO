sk-3BE3FABCDEF
sk-3BE3FABCDEF

# Step 1: Create a task
curl -X POST "http://localhost:9094/api/v1/agents/1112efb4-5d41-4fc0-ae39-809ae18a163f/tasks" \
  -H "Authorization: Bearer sk-YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "API Test Task",
    "description": "Testing the API"
  }'

# Response will include: {"id": "task-uuid-here", ...}

# Step 2: Send message to the task
curl -X POST "http://localhost:9094/agents/1112efb4-5d41-4fc0-ae39-809ae18a163f/tasks/fd67a23f-d86b-4995-acf6-d3f420aa12e5/message" \
  -H "Authorization: Bearer sk-3BE3FABCDEF" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [{
      "role": "user",
      "content": "Hello! Can you help me?"
    }]
  }'


  
-----

