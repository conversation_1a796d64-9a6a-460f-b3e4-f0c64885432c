{"family": "lecca-io-server-core-task-definition", "containerDefinitions": [{"name": "lecca-io-server-core", "image": "ghcr.io/lecca-digital/server-core:latest", "cpu": 1024, "memory": 3072, "memoryReservation": 1024, "portMappings": [{"name": "lecca-io-server-core-9094-tcp", "containerPort": 9094, "hostPort": 9094, "protocol": "tcp"}], "essential": true, "environment": [{"name": "ENVIRONMENT", "value": "production"}], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "DATABASE_URL", "valueFrom": "{{ DATABASE_URL_ARN }}"}, {"name": "PARAM_STORE_ACCESS_KEY", "valueFrom": "{{ PARAM_STORE_ACCESS_KEY_ARN }}"}, {"name": "PARAM_STORE_SECRET_ACCESS_KEY", "valueFrom": "{{ PARAM_STORE_SECRET_ACCESS_KEY_ARN }}"}, {"name": "PARAM_STORE_REGION", "valueFrom": "{{ PARAM_STORE_REGION_ARN }}"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/lecca-io-server-core-task-definition", "awslogs-create-group": "true", "awslogs-region": "us-east-2", "awslogs-stream-prefix": "ecs"}, "secretOptions": []}, "systemControls": []}, {"name": "lecca-io-server-core-nginx", "image": "730335588062.dkr.ecr.us-east-2.amazonaws.com/server-core-nginx:latest", "cpu": 256, "memory": 256, "links": ["lecca-io-server-core"], "portMappings": [{"name": "lecca-io-server-core-nginx-80-tcp", "containerPort": 80, "hostPort": 80, "protocol": "tcp"}, {"name": "lecca-io-server-core-nginx-443-tcp", "containerPort": 443, "hostPort": 443, "protocol": "tcp"}], "essential": true, "environment": [], "mountPoints": [{"sourceVolume": "letsencrypt", "containerPath": "/etc/letsencrypt", "readOnly": false}], "volumesFrom": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/lecca-io-server-core-task-definition", "awslogs-create-group": "true", "awslogs-region": "us-east-2", "awslogs-stream-prefix": "ecs"}, "secretOptions": []}, "systemControls": []}], "taskRoleArn": "{{ TASK_ROLE_ARN }}", "executionRoleArn": "{{ EXECUTION_ROLE_ARN }}", "networkMode": "bridge", "volumes": [{"name": "letsencrypt", "dockerVolumeConfiguration": {"scope": "shared", "autoprovision": true, "driver": "local"}}], "requiresCompatibilities": ["EC2"], "cpu": "2048", "memory": "3584", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}}