import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsOptional, IsString, MaxLength, MinLength } from 'class-validator';

export class UpdateAgentApiKeyDto {
  @ApiProperty({
    description: 'Name for the API key',
    example: 'Production API Key',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  name?: string;

  @ApiProperty({
    description: 'Optional description for the API key',
    example: 'API key for production environment integration',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  description?: string;

  @ApiProperty({
    description: 'Whether the API key is active',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({
    description: 'Optional expiration date for the API key (ISO string)',
    example: '2024-12-31T23:59:59.000Z',
    required: false,
  })
  @IsOptional()
  @IsString()
  expiresAt?: string;

  @ApiProperty({
    description: 'Optional permissions/scopes for the API key',
    example: { canExecute: true, canRead: true, canWrite: false },
    required: false,
  })
  @IsOptional()
  permissions?: Record<string, any>;
}
