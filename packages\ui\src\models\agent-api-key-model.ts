import { z } from 'zod';

export const agentApiKeySchema = z.object({
  id: z.string().uuid(),
  createdAt: z.string(),
  updatedAt: z.string(),
  name: z.string(),
  description: z.string().optional(),
  keyPreview: z.string(),
  lastUsedAt: z.string().optional(),
  expiresAt: z.string().optional(),
  isActive: z.boolean(),
  permissions: z.record(z.any()).optional(),
  agent: z.object({
    id: z.string().uuid(),
    name: z.string(),
    description: z.string().optional(),
  }).optional(),
});

export type AgentApiKey = z.infer<typeof agentApiKeySchema>;

export const createAgentApiKeySchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  description: z.string().max(255, 'Description must be less than 255 characters').optional(),
  expiresAt: z.string().optional(),
  permissions: z.record(z.any()).optional(),
});

export type CreateAgentApiKey = z.infer<typeof createAgentApiKeySchema>;

export const updateAgentApiKeySchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters').optional(),
  description: z.string().max(255, 'Description must be less than 255 characters').optional(),
  isActive: z.boolean().optional(),
  expiresAt: z.string().optional(),
  permissions: z.record(z.any()).optional(),
});

export type UpdateAgentApiKey = z.infer<typeof updateAgentApiKeySchema>;

// Response type when creating an API key (includes the actual key)
export const agentApiKeyWithKeySchema = agentApiKeySchema.extend({
  apiKey: z.string(), // Only present when creating a new key
});

export type AgentApiKeyWithKey = z.infer<typeof agentApiKeyWithKeySchema>;
