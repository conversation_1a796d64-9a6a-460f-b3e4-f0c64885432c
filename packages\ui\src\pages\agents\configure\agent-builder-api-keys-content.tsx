import { useState } from 'react';
import { useParams } from 'react-router-dom';

import { api } from '../../../api/api-library';
import useApiMutation from '../../../api/use-api-mutation';
import useApiQuery from '../../../api/use-api-query';
import { EmptyPlaceholder } from '../../../components/empty-placeholder';
import { Icons } from '../../../components/icons';
import { Button } from '../../../components/ui/button';
import { Card } from '../../../components/ui/card';
import { Dialog } from '../../../components/ui/dialog';
import { useToast } from '../../../hooks/useToast';
import { Agent } from '../../../models/agent/agent-model';
import { AgentApiKey } from '../../../models/agent-api-key-model';
import { toLocaleDateStringOrUndefined } from '../../../utils/dates';

import { CreateApiKeyDialog } from './components/create-api-key-dialog';
import { ApiKeyDetailsDialog } from './components/api-key-details-dialog';

interface AgentBuilderApiKeysContentProps {
  agent: Agent;
}

export function AgentBuilderApiKeysContent({ agent }: AgentBuilderApiKeysContentProps) {
  const { toast } = useToast();
  const { agentId } = useParams();
  const [selectedApiKey, setSelectedApiKey] = useState<AgentApiKey | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);

  const { data: apiKeys, isLoading } = useApiQuery({
    service: 'agents',
    method: 'getApiKeys',
    apiLibraryArgs: {
      agentId: agentId!,
    },
  });

  const deleteApiKeyMutation = useApiMutation({
    service: 'agents',
    method: 'deleteApiKey',
  });

  const handleDeleteApiKey = async (apiKeyId: string) => {
    if (!confirm('Are you sure you want to delete this API key? This action cannot be undone.')) {
      return;
    }

    try {
      await deleteApiKeyMutation.mutateAsync({
        agentId: agentId!,
        apiKeyId,
      });
      toast({ title: 'API key deleted successfully' });
    } catch (error) {
      toast({ 
        title: 'Failed to delete API key', 
        variant: 'destructive',
        description: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    }
  };

  const handleViewDetails = (apiKey: AgentApiKey) => {
    setSelectedApiKey(apiKey);
    setShowDetailsDialog(true);
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
        <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
        <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-start">
        <div>
          <h3 className="text-lg font-medium">API Keys</h3>
          <p className="text-sm text-muted-foreground mt-1">
            Generate API keys to access this agent externally. Keep your API keys secure and never share them publicly.
          </p>
        </div>
        <Button
          onClick={() => setShowCreateDialog(true)}
          variant="outline"
          size="sm"
        >
          <Icons.plus className="w-4 h-4 mr-2" />
          Generate API Key
        </Button>
      </div>

      {apiKeys && apiKeys.length > 0 ? (
        <div className="space-y-4">
          {apiKeys.map((apiKey) => (
            <ApiKeyCard
              key={apiKey.id}
              apiKey={apiKey}
              onDelete={() => handleDeleteApiKey(apiKey.id)}
              onViewDetails={() => handleViewDetails(apiKey)}
            />
          ))}
        </div>
      ) : (
        <EmptyPlaceholder
          icon={<Icons.key />}
          title="No API Keys"
          description="Generate an API key to access this agent externally through the API."
          buttonLabel="Generate API Key"
          onClick={() => setShowCreateDialog(true)}
        />
      )}

      {/* Create API Key Dialog */}
      <CreateApiKeyDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        agentId={agentId!}
        agentName={agent.name}
      />

      {/* API Key Details Dialog */}
      {selectedApiKey && (
        <ApiKeyDetailsDialog
          open={showDetailsDialog}
          onOpenChange={setShowDetailsDialog}
          apiKey={selectedApiKey}
          agentId={agentId!}
        />
      )}
    </div>
  );
}

interface ApiKeyCardProps {
  apiKey: AgentApiKey;
  onDelete: () => void;
  onViewDetails: () => void;
}

function ApiKeyCard({ apiKey, onDelete, onViewDetails }: ApiKeyCardProps) {
  const isExpired = apiKey.expiresAt && new Date(apiKey.expiresAt) < new Date();
  
  return (
    <Card className="p-4">
      <div className="flex justify-between items-start">
        <div className="space-y-2 flex-1">
          <div className="flex items-center space-x-2">
            <h4 className="font-medium">{apiKey.name}</h4>
            {!apiKey.isActive && (
              <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
                Inactive
              </span>
            )}
            {isExpired && (
              <span className="px-2 py-1 text-xs bg-red-100 text-red-600 rounded">
                Expired
              </span>
            )}
          </div>
          
          {apiKey.description && (
            <p className="text-sm text-muted-foreground">{apiKey.description}</p>
          )}
          
          <div className="flex items-center space-x-4 text-xs text-muted-foreground">
            <span>Key: {apiKey.keyPreview}</span>
            <span>Created: {toLocaleDateStringOrUndefined(apiKey.createdAt)}</span>
            {apiKey.lastUsedAt && (
              <span>Last used: {toLocaleDateStringOrUndefined(apiKey.lastUsedAt)}</span>
            )}
            {apiKey.expiresAt && (
              <span>Expires: {toLocaleDateStringOrUndefined(apiKey.expiresAt)}</span>
            )}
          </div>
        </div>
        
        <div className="flex space-x-2 ml-4">
          <Button
            variant="outline"
            size="sm"
            onClick={onViewDetails}
          >
            <Icons.eye className="w-4 h-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={onDelete}
            className="text-red-600 hover:text-red-700"
          >
            <Icons.trash className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </Card>
  );
}
