import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

import useApiQuery from '../../api/use-api-query';
import { EmptyPlaceholder } from '../../components/empty-placeholder';
import { Icons } from '../../components/icons';
import PageLayout from '../../components/layouts/page-layout';
import { GridLoader } from '../../components/loaders/grid-loader';
import { PageLoader } from '../../components/loaders/page-loader';
import { Badge } from '../../components/ui/badge';
import { Button } from '../../components/ui/button';
import { Card } from '../../components/ui/card';
import { Label } from '../../components/ui/label';
import { Input } from '../../components/ui/input';
import { Sheet } from '../../components/ui/sheet';
import { Agent } from '../../models/agent/agent-model';
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '../../models/agent-api-key-model';
import { toLocaleDateStringOrUndefined } from '../../utils/dates';

import { ApiTestSheet } from './components/api-test-sheet';

export function ApiPage() {
  const [search, setSearch] = useState('');
  const [showTestSheet, setShowTestSheet] = useState(false);
  const navigate = useNavigate();

  // Get all agents
  const { data: agents, isLoading: isLoadingAgents } = useApiQuery({
    service: 'agents',
    method: 'getList',
    apiLibraryArgs: {},
  });

  // Filter agents that have API keys (we'll show all for now)
  const agentsWithApiKeys = agents || [];

  if (isLoadingAgents) {
    return (
      <PageLoader>
        <GridLoader
          itemClassName="h-36"
          className="grid gap-6 grid-cols-1 lg:grid-cols-2 2xl:grid-cols-3"
        />
      </PageLoader>
    );
  }

  const filteredAgents = agentsWithApiKeys.filter(
    (agent) =>
      agent.name.toLowerCase().includes(search.toLowerCase()) ||
      (agent.description &&
        agent.description.toLowerCase().includes(search.toLowerCase()))
  );

  return (
    <PageLayout
      title="API Management"
      subtitle="Manage API keys and external access for your agents"
      actions={[
        <Button
          variant="outline"
          onClick={() => navigate('/agents')}
        >
          <Icons.agent className="w-4 h-4 mr-2" />
          View All Agents
        </Button>
      ]}
      className="space-y-6"
    >
      {/* API Stats Summary */}
      <ApiStatsSummary agents={agentsWithApiKeys} />

      {/* API Documentation */}
      <ApiDocumentation />

      <div className="flex items-center justify-between">
        <Input
          type="search"
          placeholder="Search agents..."
          className="py-2 w-[200px] lg:w-[250px]"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
        />
        
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => setShowTestSheet(true)}
          >
            <Icons.play className="w-4 h-4 mr-2" />
            Test API
          </Button>

          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span>Active API Keys</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
              <span>No API Keys</span>
            </div>
          </div>
        </div>
      </div>

      {filteredAgents.length > 0 ? (
        <div className="grid gap-6 grid-cols-1 lg:grid-cols-2 2xl:grid-cols-3">
          {filteredAgents.map((agent) => (
            <AgentApiCard key={agent.id} agent={agent} />
          ))}
        </div>
      ) : (
        <EmptyPlaceholder
          icon={<Icons.key />}
          title="No Agents Found"
          description="No agents match your search criteria."
          buttonLabel="View All Agents"
          onClick={() => navigate('/agents')}
        />
      )}

      {/* API Test Sheet */}
      <ApiTestSheet
        open={showTestSheet}
        onOpenChange={setShowTestSheet}
        agents={agentsWithApiKeys}
      />
    </PageLayout>
  );
}

function AgentApiCard({ agent }: { agent: Agent }) {
  const navigate = useNavigate();

  // Fetch API keys for this specific agent
  const { data: apiKeys, isLoading } = useApiQuery({
    service: 'agents',
    method: 'getApiKeys',
    apiLibraryArgs: {
      agentId: agent.id,
    },
  });

  const activeApiKeys = apiKeys?.filter(key => key.isActive) || [];
  const hasApiKeys = apiKeys && apiKeys.length > 0;

  const handleManageApiKeys = () => {
    navigate(`/projects/${agent.project?.id}/agents/${agent.id}/configure?tab=api-keys`);
  };

  const handleViewAgent = () => {
    navigate(`/projects/${agent.project?.id}/agents/${agent.id}`);
  };

  return (
    <Card className="relative">
      <Card.Header>
        <Card.Title className="flex justify-between items-start">
          <div className="flex flex-col space-y-2">
            <div className="flex items-center space-x-2">
              <span className="font-medium">{agent.name}</span>
              {hasApiKeys ? (
                <div className="w-3 h-3 bg-green-500 rounded-full" title="Has API keys" />
              ) : (
                <div className="w-3 h-3 bg-gray-300 rounded-full" title="No API keys" />
              )}
            </div>
            <span className="line-clamp-2 text-sm font-normal text-muted-foreground">
              {agent.description || 'No description'}
            </span>
          </div>
        </Card.Title>
      </Card.Header>
      
      <Card.Content className="space-y-4">
        <div className="flex items-center justify-between text-sm">
          <span className="text-muted-foreground">Project:</span>
          <Badge variant="outline">{agent.project?.name}</Badge>
        </div>
        
        {isLoading ? (
          <div className="flex items-center space-x-2">
            <Icons.spinner className="w-4 h-4 animate-spin" />
            <span className="text-sm text-muted-foreground">Loading API keys...</span>
          </div>
        ) : (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Total API Keys:</span>
              <span className="font-medium">{apiKeys?.length || 0}</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Active Keys:</span>
              <span className="font-medium text-green-600">{activeApiKeys.length}</span>
            </div>
            {apiKeys && apiKeys.length > 0 && (
              <div className="text-xs text-muted-foreground">
                Last used: {
                  apiKeys.find(key => key.lastUsedAt)?.lastUsedAt 
                    ? toLocaleDateStringOrUndefined(
                        apiKeys
                          .filter(key => key.lastUsedAt)
                          .sort((a, b) => new Date(b.lastUsedAt!).getTime() - new Date(a.lastUsedAt!).getTime())[0]
                          ?.lastUsedAt
                      )
                    : 'Never'
                }
              </div>
            )}
          </div>
        )}
      </Card.Content>
      
      <Card.Footer className="flex justify-between">
        <Button
          variant="outline"
          size="sm"
          onClick={handleViewAgent}
        >
          <Icons.eye className="w-4 h-4 mr-2" />
          View Agent
        </Button>
        <Button
          variant="default"
          size="sm"
          onClick={handleManageApiKeys}
        >
          <Icons.key className="w-4 h-4 mr-2" />
          {hasApiKeys ? 'Manage Keys' : 'Generate Key'}
        </Button>
      </Card.Footer>
    </Card>
  );
}

function ApiStatsSummary({ agents }: { agents: Agent[] }) {
  // This is a placeholder for future API usage statistics
  // You can later add charts and detailed analytics here

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <Card.Header className="flex flex-row items-center justify-between space-y-0 pb-2">
          <Card.Title className="text-sm font-medium">Total Agents</Card.Title>
          <Icons.agent className="h-4 w-4 text-muted-foreground" />
        </Card.Header>
        <Card.Content>
          <div className="text-2xl font-bold">{agents.length}</div>
          <p className="text-xs text-muted-foreground">
            Agents in your workspace
          </p>
        </Card.Content>
      </Card>

      <Card>
        <Card.Header className="flex flex-row items-center justify-between space-y-0 pb-2">
          <Card.Title className="text-sm font-medium">API Enabled</Card.Title>
          <Icons.key className="h-4 w-4 text-muted-foreground" />
        </Card.Header>
        <Card.Content>
          <div className="text-2xl font-bold">-</div>
          <p className="text-xs text-muted-foreground">
            Agents with API keys
          </p>
        </Card.Content>
      </Card>

      <Card>
        <Card.Header className="flex flex-row items-center justify-between space-y-0 pb-2">
          <Card.Title className="text-sm font-medium">Total API Keys</Card.Title>
          <Icons.key className="h-4 w-4 text-muted-foreground" />
        </Card.Header>
        <Card.Content>
          <div className="text-2xl font-bold">-</div>
          <p className="text-xs text-muted-foreground">
            Active API keys
          </p>
        </Card.Content>
      </Card>

      <Card>
        <Card.Header className="flex flex-row items-center justify-between space-y-0 pb-2">
          <Card.Title className="text-sm font-medium">API Calls</Card.Title>
          <Icons.activity className="h-4 w-4 text-muted-foreground" />
        </Card.Header>
        <Card.Content>
          <div className="text-2xl font-bold">-</div>
          <p className="text-xs text-muted-foreground">
            This month
          </p>
        </Card.Content>
      </Card>
    </div>
  );
}

function ApiDocumentation() {
  return (
    <Card>
      <Card.Header>
        <Card.Title>API Documentation</Card.Title>
        <Card.Description>
          Learn how to integrate your agents with external applications using our REST API.
        </Card.Description>
      </Card.Header>
      <Card.Content className="space-y-6">
        {/* Authentication */}
        <div className="space-y-3">
          <h4 className="text-sm font-semibold">Authentication</h4>
          <p className="text-sm text-muted-foreground">
            All API requests require authentication using Bearer tokens. Include your API key in the Authorization header:
          </p>
          <div className="p-3 bg-muted rounded-lg">
            <code className="text-sm">Authorization: Bearer sk-YOUR_API_KEY</code>
          </div>
        </div>

        {/* Important Note */}
        <div className="space-y-3">
          <h4 className="text-sm font-semibold">Important: Two-Step Process</h4>
          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <p className="text-sm text-blue-800 dark:text-blue-200">
              <strong>Chat requires 2 steps:</strong> First create a task, then send messages to that task.
              This allows multiple conversations per agent and better organization.
            </p>
          </div>
        </div>

        {/* Base URL */}
        <div className="space-y-3">
          <h4 className="text-sm font-semibold">Base URL</h4>
          <div className="p-3 bg-muted rounded-lg">
            <code className="text-sm">http://localhost:9094/api/v1</code>
          </div>
        </div>

        {/* Endpoints */}
        <div className="space-y-4">
          <h4 className="text-sm font-semibold">Available Endpoints</h4>

          <div className="space-y-4">
            {/* Create Task Endpoint */}
            <div className="border rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Badge variant="default">POST</Badge>
                <code className="text-sm">/api/v1/agents/{`{agentId}`}/tasks</code>
              </div>
              <p className="text-sm text-muted-foreground mb-3">
                Create a new task for an agent.
              </p>
              <div className="space-y-2">
                <p className="text-xs font-medium">Request Body:</p>
                <pre className="text-xs bg-muted p-3 rounded overflow-auto">
{`{
  "name": "My Task",
  "description": "Task description"
}`}
                </pre>
              </div>
            </div>

            {/* Message Task Endpoint */}
            <div className="border rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Badge variant="default">POST</Badge>
                <code className="text-sm">/api/v1/agents/{`{agentId}`}/tasks/{`{taskId}`}/message</code>
              </div>
              <p className="text-sm text-muted-foreground mb-3">
                Send a message to an agent task and receive a response.
              </p>
              <div className="space-y-2">
                <p className="text-xs font-medium">Request Body:</p>
                <pre className="text-xs bg-muted p-3 rounded overflow-auto">
{`{
  "messages": [{
    "role": "user",
    "content": "Hello! Can you help me?"
  }]
}`}
                </pre>
              </div>
            </div>

            {/* Agent Info Endpoint */}
            <div className="border rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Badge variant="secondary">GET</Badge>
                <code className="text-sm">/api/v1/agents/{`{agentId}`}</code>
              </div>
              <p className="text-sm text-muted-foreground">
                Get information about a specific agent.
              </p>
            </div>
          </div>
        </div>

        {/* Code Examples */}
        <div className="space-y-4">
          <h4 className="text-sm font-semibold">Code Examples</h4>

          {/* cURL Example */}
          <div className="space-y-2">
            <Label className="text-xs font-medium">cURL (Command Line)</Label>
            <div className="p-3 bg-muted rounded-lg">
              <pre className="text-xs overflow-auto">
{`# Step 1: Create a task
curl -X POST "http://localhost:9094/api/v1/agents/YOUR_AGENT_ID/tasks" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{"name": "My Task", "description": "Task description"}'

# Step 2: Send message (replace TASK_ID with the id from step 1)
curl -X POST "http://localhost:9094/api/v1/agents/YOUR_AGENT_ID/tasks/TASK_ID/message" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{"messages": [{"role": "user", "content": "Hello! Can you help me?"}]}'`}
              </pre>
            </div>
          </div>

          {/* JavaScript/React Example */}
          <div className="space-y-2">
            <Label className="text-xs font-medium">JavaScript / React</Label>
            <div className="p-3 bg-muted rounded-lg">
              <pre className="text-xs overflow-auto">
{`async function chatWithAgent(agentId, message, apiKey) {
  const baseUrl = 'http://localhost:9094';

  // Step 1: Create a task
  const taskResponse = await fetch(\`\${baseUrl}/api/v1/agents/\${agentId}/tasks\`, {
    method: 'POST',
    headers: {
      'Authorization': \`Bearer \${apiKey}\`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      name: 'Chat Task',
      description: 'User conversation'
    })
  });

  const task = await taskResponse.json();

  // Step 2: Send message
  const messageResponse = await fetch(\`\${baseUrl}/api/v1/agents/\${agentId}/tasks/\${task.id}/message\`, {
    method: 'POST',
    headers: {
      'Authorization': \`Bearer \${apiKey}\`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      messages: [{
        role: 'user',
        content: message
      }]
    })
  });

  return await messageResponse.json();
}

// Usage
chatWithAgent('YOUR_AGENT_ID', 'Hello!', 'YOUR_API_KEY')
  .then(response => console.log(response));`}
              </pre>
            </div>
          </div>

          {/* Python Example */}
          <div className="space-y-2">
            <Label className="text-xs font-medium">Python</Label>
            <div className="p-3 bg-muted rounded-lg">
              <pre className="text-xs overflow-auto">
{`import requests

def chat_with_agent(agent_id, message, api_key):
    base_url = 'http://localhost:9094'
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }

    # Step 1: Create a task
    task_response = requests.post(
        f'{base_url}/api/v1/agents/{agent_id}/tasks',
        headers=headers,
        json={
            'name': 'Chat Task',
            'description': 'User conversation'
        }
    )
    task = task_response.json()

    # Step 2: Send message
    message_response = requests.post(
        f'{base_url}/api/v1/agents/{agent_id}/tasks/{task["id"]}/message',
        headers=headers,
        json={
            'messages': [{
                'role': 'user',
                'content': message
            }]
        }
    )

    return message_response.json()

# Usage
response = chat_with_agent('YOUR_AGENT_ID', 'Hello!', 'YOUR_API_KEY')
print(response)`}
              </pre>
            </div>
          </div>
        </div>
      </Card.Content>
    </Card>
  );
}
