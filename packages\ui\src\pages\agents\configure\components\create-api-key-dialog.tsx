import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useState } from 'react';
import { useForm } from 'react-hook-form';

import useApiMutation from '../../../../api/use-api-mutation';
import { Icons } from '../../../../components/icons';
import { Button } from '../../../../components/ui/button';
import { Dialog } from '../../../../components/ui/dialog';
import { Form } from '../../../../components/ui/form';
import { Input } from '../../../../components/ui/input';
import { Label } from '../../../../components/ui/label';
import { Textarea } from '../../../../components/ui/textarea';
import { useToast } from '../../../../hooks/useToast';
import { CreateAgentApiKey, createAgentApiKeySchema } from '../../../../models/agent-api-key-model';

interface CreateApiKeyDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  agentId: string;
  agentName: string;
}

export function CreateApiKeyDialog({ 
  open, 
  onOpenChange, 
  agentId, 
  agentName 
}: CreateApiKeyDialogProps) {
  const { toast } = useToast();
  const [generatedApiKey, setGeneratedApiKey] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);

  const form = useForm<CreateAgentApiKey>({
    resolver: zodResolver(createAgentApiKeySchema),
    defaultValues: {
      name: '',
      description: '',
    },
  });

  const createApiKeyMutation = useApiMutation({
    service: 'agents',
    method: 'generateApiKey',
  });

  const handleSubmit = async (data: CreateAgentApiKey) => {
    try {
      const response = await createApiKeyMutation.mutateAsync({
        agentId,
        data,
      });

      if (response.data) {
        setGeneratedApiKey(response.data.apiKey);
        toast({ title: 'API key generated successfully' });
      }
    } catch (error) {
      toast({
        title: 'Failed to generate API key',
        variant: 'destructive',
        description: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    }
  };

  const handleCopyApiKey = async () => {
    if (generatedApiKey) {
      try {
        await navigator.clipboard.writeText(generatedApiKey);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
        toast({ title: 'API key copied to clipboard' });
      } catch (error) {
        toast({
          title: 'Failed to copy API key',
          variant: 'destructive',
        });
      }
    }
  };

  const handleClose = () => {
    setGeneratedApiKey(null);
    setCopied(false);
    form.reset();
    onOpenChange(false);
  };

  if (generatedApiKey) {
    return (
      <Dialog open={open} onOpenChange={handleClose}>
        <Dialog.Content className="max-w-2xl">
          <Dialog.Header>
            <Dialog.Title>API Key Generated</Dialog.Title>
            <Dialog.Description>
              Your API key has been generated successfully. Copy it now as it won't be shown again.
            </Dialog.Description>
          </Dialog.Header>

          <div className="space-y-4">
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-start space-x-2">
                <Icons.alertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
                <div className="text-sm text-yellow-800">
                  <p className="font-medium">Important Security Notice</p>
                  <p>This is the only time you'll see this API key. Store it securely and never share it publicly.</p>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Your API Key</Label>
              <div className="flex space-x-2">
                <Input
                  value={generatedApiKey}
                  readOnly
                  className="font-mono text-sm"
                />
                <Button
                  onClick={handleCopyApiKey}
                  variant="outline"
                  size="sm"
                >
                  {copied ? (
                    <>
                      <Icons.check className="w-4 h-4 mr-2" />
                      Copied
                    </>
                  ) : (
                    <>
                      <Icons.copy className="w-4 h-4 mr-2" />
                      Copy
                    </>
                  )}
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Usage Example</Label>
              <div className="p-3 bg-gray-50 rounded-lg">
                <code className="text-sm">
                  curl -H "Authorization: Bearer {generatedApiKey}" \<br />
                  &nbsp;&nbsp;&nbsp;&nbsp; -H "Content-Type: application/json" \<br />
                  &nbsp;&nbsp;&nbsp;&nbsp; -d '{`{"name": "My Task", "description": "Task description"}`}' \<br />
                  &nbsp;&nbsp;&nbsp;&nbsp; https://your-domain.com/api/v1/agents/{agentId}/tasks
                </code>
              </div>
            </div>
          </div>

          <Dialog.Footer>
            <Button onClick={handleClose}>
              I've Saved My API Key
            </Button>
          </Dialog.Footer>
        </Dialog.Content>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <Dialog.Content>
        <Dialog.Header>
          <Dialog.Title>Generate API Key</Dialog.Title>
          <Dialog.Description>
            Create a new API key for external access to "{agentName}".
          </Dialog.Description>
        </Dialog.Header>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Name *</Label>
              <Input
                id="name"
                placeholder="e.g., Production API Key"
                {...form.register('name')}
              />
              {form.formState.errors.name && (
                <p className="text-sm text-red-600">{form.formState.errors.name.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Optional description for this API key"
                {...form.register('description')}
              />
              {form.formState.errors.description && (
                <p className="text-sm text-red-600">{form.formState.errors.description.message}</p>
              )}
            </div>

            <Dialog.Footer>
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                loading={createApiKeyMutation.isPending}
              >
                Generate API Key
              </Button>
            </Dialog.Footer>
          </form>
        </Form>
      </Dialog.Content>
    </Dialog>
  );
}
