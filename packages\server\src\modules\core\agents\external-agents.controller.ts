import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Res,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiSecurity, ApiBearerAuth } from '@nestjs/swagger';
import { Response } from 'express';
import { v4 as uuidv4 } from 'uuid';

import { User } from '../../../decorators/user.decorator';
import { ApiKeyGuard } from '../../../guards/api-key.guard';
import { JwtUser } from '../../../types/jwt-user.type';
import { CreateTaskDto } from '../tasks/dto/create-task.dto';
import { MessageTaskDto } from '../tasks/dto/message-task.dto';
import { TasksService } from '../tasks/tasks.service';

import { AgentsService } from './agents.service';

@Controller('api/v1/agents')
@ApiTags('External Agent API')
@ApiSecurity('api-key')
@ApiBearerAuth()
@UseGuards(ApiKeyGuard)
export class ExternalAgentsController {
  constructor(
    private readonly agentsService: AgentsService,
    private readonly tasksService: TasksService,
  ) {}

  @Get('test')
  async testApiKey(@User() user: JwtUser) {
    return {
      message: 'API key authentication successful!',
      user: {
        isApiKeyAuth: user.isApiKeyAuth,
        agentId: user.agentId,
        workspaceId: user.workspaceId,
        roles: user.roles,
      },
    };
  }

  @Get(':agentId')
  async getAgent(
    @Param('agentId') agentId: string,
    @User() user: JwtUser,
  ) {
    // Verify the API key has access to this specific agent
    if (user.isApiKeyAuth && user.agentId !== agentId) {
      throw new Error('API key does not have access to this agent');
    }

    return this.agentsService.findOne({
      agentId,
      expansion: {
        expansion: ['name', 'description', 'instructions'],
      } as any,
      throwNotFoundException: true,
    });
  }

  @Post(':agentId/tasks')
  async createTask(
    @Param('agentId') agentId: string,
    @Body() data: CreateTaskDto,
    @User() user: JwtUser,
  ) {
    // Verify the API key has access to this specific agent
    if (user.isApiKeyAuth && user.agentId !== agentId) {
      throw new Error('API key does not have access to this agent');
    }

    return this.tasksService.create({
      agentId,
      data,
      expansion: {
        expansion: ['name', 'description', 'messages'],
      } as any,
    });
  }

  @Post(':agentId/tasks/:taskId/message')
  async messageTask(
    @Param('agentId') agentId: string,
    @Param('taskId') taskId: string,
    @Body() data: MessageTaskDto,
    @User() user: JwtUser,
    @Res() response: Response,
  ) {
    // Verify the API key has access to this specific agent
    if (user.isApiKeyAuth && user.agentId !== agentId) {
      throw new Error('API key does not have access to this agent');
    }

    const messages = data.messages?.length > 0 
      ? [data.messages[data.messages.length - 1]] 
      : [];

    const result = await this.tasksService.messageTaskOrCreateTaskIfNotFound({
      agentId,
      taskId,
      messages,
      requestingWorkspaceUserId: user.workspaceUserId,
      workspaceId: user.workspaceId,
      customIdentifier: undefined,
      shouldStream: false,
      simpleResponse: false,
    });

    if (typeof result === 'string' || Array.isArray(result)) {
      return response.status(200).json(result);
    } else {
      return result.pipeDataStreamToResponse(response);
    }
  }

  @Post(':agentId/tasks/:taskId/stream-message')
  async streamMessageTask(
    @Param('agentId') agentId: string,
    @Param('taskId') taskId: string,
    @Body() data: MessageTaskDto,
    @User() user: JwtUser,
    @Res() response: Response,
  ) {
    // Verify the API key has access to this specific agent
    if (user.isApiKeyAuth && user.agentId !== agentId) {
      throw new Error('API key does not have access to this agent');
    }

    try {
      const messages = data.messages?.length > 0 
        ? [data.messages[data.messages.length - 1]] 
        : [];

      const result = await this.tasksService.messageTaskOrCreateTaskIfNotFound({
        agentId,
        taskId,
        messages,
        requestingWorkspaceUserId: user.workspaceUserId,
        workspaceId: user.workspaceId,
        shouldStream: true,
        customIdentifier: undefined,
      });

      if (typeof result === 'string' || Array.isArray(result)) {
        return response.status(200).json(result);
      } else {
        return result.pipeDataStreamToResponse(response, {
          getErrorMessage(error) {
            if (typeof error === 'object') {
              return JSON.stringify(error);
            } else {
              return error?.toString();
            }
          },
        });
      }
    } catch (error) {
      return response.status(500).json({ error: error.message });
    }
  }

  @Post(':agentId/chat')
  async chatWithAgent(
    @Param('agentId') agentId: string,
    @Body() data: { message: string; sessionId?: string },
    @User() user: JwtUser,
  ) {
    // Verify the API key has access to this specific agent
    if (user.isApiKeyAuth && user.agentId !== agentId) {
      throw new Error('API key does not have access to this agent');
    }

    try {
      // Step 1: Create a task automatically
      const task = await this.tasksService.create({
        agentId,
        data: {
          name: data.sessionId ? `Chat Session ${data.sessionId}` : 'API Chat',
          description: 'Auto-created task for API chat',
        },
        expansion: {
          expansion: ['name', 'description', 'messages'],
        } as any,
      });

      // Step 2: Send the message to the task
      const result = await this.tasksService.messageTaskOrCreateTaskIfNotFound({
        agentId,
        taskId: task.id,
        messages: [{
          id: uuidv4(),
          role: 'user',
          content: data.message,
        }],
        requestingWorkspaceUserId: user.workspaceUserId,
        workspaceId: user.workspaceId,
        customIdentifier: undefined,
        shouldStream: false,
      });

      return {
        taskId: task.id,
        response: result,
      };
    } catch (error) {
      return { error: error.message };
    }
  }
}
