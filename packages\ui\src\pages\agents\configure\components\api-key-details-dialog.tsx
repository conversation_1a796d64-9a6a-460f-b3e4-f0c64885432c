import { zodResolver } from '@hookform/resolvers/zod';
import { useState } from 'react';
import { useForm } from 'react-hook-form';

import useApiMutation from '../../../../api/use-api-mutation';
import { Icons } from '../../../../components/icons';
import { Button } from '../../../../components/ui/button';
import { Dialog } from '../../../../components/ui/dialog';
import { Form } from '../../../../components/ui/form';
import { Input } from '../../../../components/ui/input';
import { Label } from '../../../../components/ui/label';
import { Switch } from '../../../../components/ui/switch';
import { Textarea } from '../../../../components/ui/textarea';
import { useToast } from '../../../../hooks/useToast';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, UpdateAgentApi<PERSON>ey, updateAgentApiKeySchema } from '../../../../models/agent-api-key-model';
import { toLocaleDateStringOrUndefined } from '../../../../utils/dates';

interface ApiKeyDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  apiKey: AgentApiKey;
  agentId: string;
}

export function ApiKeyDetailsDialog({ 
  open, 
  onOpenChange, 
  apiKey, 
  agentId 
}: ApiKeyDetailsDialogProps) {
  const { toast } = useToast();
  const [isEditing, setIsEditing] = useState(false);

  const form = useForm<UpdateAgentApiKey>({
    resolver: zodResolver(updateAgentApiKeySchema),
    defaultValues: {
      name: apiKey.name,
      description: apiKey.description || '',
      isActive: apiKey.isActive,
    },
  });

  const updateApiKeyMutation = useApiMutation({
    service: 'agents',
    method: 'updateApiKey',
  });

  const handleSubmit = async (data: UpdateAgentApiKey) => {
    try {
      await updateApiKeyMutation.mutateAsync({
        agentId,
        apiKeyId: apiKey.id,
        data,
      });
      
      toast({ title: 'API key updated successfully' });
      setIsEditing(false);
      onOpenChange(false);
    } catch (error) {
      toast({
        title: 'Failed to update API key',
        variant: 'destructive',
        description: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    }
  };

  const handleClose = () => {
    setIsEditing(false);
    form.reset({
      name: apiKey.name,
      description: apiKey.description || '',
      isActive: apiKey.isActive,
    });
    onOpenChange(false);
  };

  const isExpired = apiKey.expiresAt && new Date(apiKey.expiresAt) < new Date();

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <Dialog.Content>
        <Dialog.Header>
          <Dialog.Title>API Key Details</Dialog.Title>
          <Dialog.Description>
            View and manage this API key's settings.
          </Dialog.Description>
        </Dialog.Header>

        {isEditing ? (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Name *</Label>
                <Input
                  id="name"
                  {...form.register('name')}
                />
                {form.formState.errors.name && (
                  <p className="text-sm text-red-600">{form.formState.errors.name.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  {...form.register('description')}
                />
                {form.formState.errors.description && (
                  <p className="text-sm text-red-600">{form.formState.errors.description.message}</p>
                )}
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={form.watch('isActive')}
                  onCheckedChange={(checked) => form.setValue('isActive', checked)}
                />
                <Label htmlFor="isActive">Active</Label>
              </div>

              <Dialog.Footer>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsEditing(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  loading={updateApiKeyMutation.isPending}
                >
                  Save Changes
                </Button>
              </Dialog.Footer>
            </form>
          </Form>
        ) : (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Name</Label>
                <p className="mt-1">{apiKey.name}</p>
              </div>
              
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Status</Label>
                <div className="mt-1 flex items-center space-x-2">
                  {apiKey.isActive ? (
                    <span className="px-2 py-1 text-xs bg-green-100 text-green-600 rounded">
                      Active
                    </span>
                  ) : (
                    <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
                      Inactive
                    </span>
                  )}
                  {isExpired && (
                    <span className="px-2 py-1 text-xs bg-red-100 text-red-600 rounded">
                      Expired
                    </span>
                  )}
                </div>
              </div>
            </div>

            {apiKey.description && (
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Description</Label>
                <p className="mt-1">{apiKey.description}</p>
              </div>
            )}

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Key Preview</Label>
                <p className="mt-1 font-mono text-sm">{apiKey.keyPreview}</p>
              </div>
              
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Created</Label>
                <p className="mt-1">{toLocaleDateStringOrUndefined(apiKey.createdAt)}</p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              {apiKey.lastUsedAt && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Last Used</Label>
                  <p className="mt-1">{toLocaleDateStringOrUndefined(apiKey.lastUsedAt)}</p>
                </div>
              )}
              
              {apiKey.expiresAt && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Expires</Label>
                  <p className="mt-1">{toLocaleDateStringOrUndefined(apiKey.expiresAt)}</p>
                </div>
              )}
            </div>

            <div className="p-3 bg-gray-50 rounded-lg">
              <Label className="text-sm font-medium text-muted-foreground">Usage Example</Label>
              <code className="block mt-2 text-sm">
                curl -H "Authorization: Bearer YOUR_API_KEY" \<br />
                &nbsp;&nbsp;&nbsp;&nbsp; -H "Content-Type: application/json" \<br />
                &nbsp;&nbsp;&nbsp;&nbsp; -d '{`{"name": "My Task"}`}' \<br />
                &nbsp;&nbsp;&nbsp;&nbsp; https://your-domain.com/api/v1/agents/{agentId}/tasks
              </code>
            </div>

            <Dialog.Footer>
              <Button
                variant="outline"
                onClick={handleClose}
              >
                Close
              </Button>
              <Button
                onClick={() => setIsEditing(true)}
              >
                <Icons.edit className="w-4 h-4 mr-2" />
                Edit
              </Button>
            </Dialog.Footer>
          </div>
        )}
      </Dialog.Content>
    </Dialog>
  );
}
